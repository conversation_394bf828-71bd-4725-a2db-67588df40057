<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ethnic Resemblance Analyzer</title>
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #1a202c;
            /* Dark background */
            color: #e2e8f0;
            /* Light text */
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
            box-sizing: border-box;
        }

        .container {
            background-color: #2d3748;
            /* Slightly lighter dark background for container */
            border-radius: 1.5rem;
            /* More rounded corners */
            box-shadow: 0 10px 15px rgba(0, 0, 0, 0.2);
            padding: 2.5rem;
            max-width: 900px;
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 1.5rem;
        }

        .main-display-area {
            position: relative;
            width: 100%;
            max-width: 600px;
            /* Overall size of the display area including the grid */
            aspect-ratio: 1 / 1;
            /* Keep it square for the grid */
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .ethnic-grid {
            display: grid;
            grid-template-columns: repeat(8, 1fr);
            /* 8 columns */
            grid-template-rows: repeat(8, 1fr);
            /* 8 rows */
            width: 100%;
            height: 100%;
            gap: 5px;
            /* Small gap between boxes */
            padding: 10px;
            /* Padding inside the grid container */
            box-sizing: border-box;
            position: relative;
            z-index: 2;
            /* Above background overlay */
        }

        .ethnic-box {
            width: 100%;
            height: 100%;
            aspect-ratio: 1 / 1;
            border-radius: 50%;
            background-image: url('images.jpeg');
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            border: 2px solid rgba(66, 153, 225, 0.4);
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 0.7rem;
            color: #a0aec0;
            overflow: hidden;
            /* Ensure content is clipped if any */
            opacity: 0;
            animation: fade-in-scale 0.5s forwards;
            /* Animation for each box */
            position: relative;
        }

        .ethnic-box::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(26, 32, 44, 0.6);
            /* Dark overlay for better contrast */
            border-radius: 50%;
            z-index: 1;
        }

        .ethnic-box::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle, transparent 30%, rgba(66, 153, 225, 0.2) 70%);
            border-radius: 50%;
            z-index: 2;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .ethnic-box:hover::after {
            opacity: 1;
        }

        @keyframes fade-in-scale {
            from {
                opacity: 0;
                transform: scale(0.8);
            }

            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        /* Delay animation for each box */
        .ethnic-box:nth-child(even) {
            animation-delay: 0.1s;
        }

        .ethnic-box:nth-child(3n) {
            animation-delay: 0.2s;
        }

        .ethnic-box:nth-child(5n) {
            animation-delay: 0.3s;
        }


        .canvas-container {
            position: absolute;
            width: 60%;
            /* Adjust size relative to main-display-area */
            height: 60%;
            /* Adjust size relative to main-display-area */
            max-width: 400px;
            /* Adjusted max-width for a better circular appearance */
            aspect-ratio: 1 / 1;
            /* Make it a perfect circle */
            background-color: #000;
            border-radius: 50%;
            /* Make it round */
            overflow: hidden;
            /* Crucial for clipping content to the circle */
            display: flex;
            justify-content: center;
            align-items: center;
            border: 2px solid #4a5568;
            z-index: 50;
            /* Ensure it's above the grid */
            box-shadow: 0 0 30px rgba(66, 153, 225, 0.6);
            /* Add a glow */
        }

        video,
        canvas {
            width: 100%;
            height: 100%;
            object-fit: cover;
            /* Ensures video/image covers the circular container */
            border-radius: 50%;
            /* Apply to content as well for consistency */
        }

        .scanning-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border: 4px solid transparent;
            box-sizing: border-box;
            border-radius: 50%;
            /* Make it round */
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.3s ease-in-out;
            z-index: 10;
        }

        .scanning-overlay.active {
            opacity: 1;
            animation: pulse-border 1.5s infinite alternate;
            /* Keep pulse */
        }

        /* Enhanced scan line animation */
        .scanning-overlay::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(to bottom, transparent 0%, transparent 45%, rgba(66, 153, 225, 0.7) 50%, transparent 55%, transparent 100%);
            background-size: 100% 200%;
            background-position: 0% 0%;
            animation: scan-line-v2 2s infinite linear;
            opacity: 0;
            transition: opacity 0.3s ease-in-out;
            border-radius: 50%;
            /* Make scan line round */
        }

        .scanning-overlay.active::before {
            opacity: 1;
        }

        /* Data glitch effect overlay */
        .scanning-overlay::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: repeating-linear-gradient(90deg,
                    transparent,
                    transparent 2px,
                    rgba(66, 153, 225, 0.1) 2px,
                    rgba(66, 153, 225, 0.1) 4px);
            opacity: 0;
            animation: glitch-effect 0.3s infinite;
            border-radius: 50%;
        }

        .scanning-overlay.active::after {
            opacity: 1;
        }

        @keyframes pulse-border {
            0% {
                border-color: rgba(66, 153, 225, 0.5);
                box-shadow: 0 0 15px rgba(66, 153, 225, 0.5);
            }

            100% {
                border-color: rgba(66, 153, 225, 1);
                box-shadow: 0 0 25px rgba(66, 153, 225, 1);
            }
        }

        @keyframes scan-line-v2 {
            0% {
                background-position: 0% 0%;
            }

            100% {
                background-position: 0% 100%;
            }
        }

        @keyframes glitch-effect {

            0%,
            90%,
            100% {
                opacity: 0;
            }

            5%,
            85% {
                opacity: 0.3;
                transform: translateX(0);
            }

            10%,
            80% {
                opacity: 0.6;
                transform: translateX(2px);
            }

            15%,
            75% {
                opacity: 0.3;
                transform: translateX(-1px);
            }

            20%,
            70% {
                opacity: 0.8;
                transform: translateX(1px);
            }
        }

        .processing-message {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: rgba(0, 0, 0, 0.8);
            padding: 1.5rem 2rem;
            border-radius: 1rem;
            font-size: 1.2rem;
            font-weight: bold;
            color: #4299e1;
            opacity: 0;
            transition: opacity 0.3s ease-in-out;
            z-index: 20;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 1rem;
            text-align: center;
            min-width: 280px;
            justify-content: center;
            border: 2px solid rgba(66, 153, 225, 0.3);
            box-shadow: 0 0 20px rgba(66, 153, 225, 0.2);
        }

        .processing-message.active {
            opacity: 1;
        }

        .processing-header {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .spinner {
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid #4299e1;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            animation: spin 1s linear infinite;
        }

        .progress-container {
            width: 100%;
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            height: 8px;
            overflow: hidden;
        }

        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #4299e1, #63b3ed);
            border-radius: 10px;
            width: 0%;
            transition: width 0.3s ease;
            animation: progress-glow 2s infinite alternate;
        }

        .typewriter-text {
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            color: #68d391;
            min-height: 1.2rem;
            border-right: 2px solid #68d391;
            animation: blink-cursor 1s infinite;
        }

        .data-stream {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 15;
        }

        .data-particle {
            position: absolute;
            width: 3px;
            height: 3px;
            background-color: #4299e1;
            border-radius: 50%;
            opacity: 0;
            animation: data-flow 2s infinite linear;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        @keyframes progress-glow {
            0% {
                box-shadow: 0 0 5px rgba(66, 153, 225, 0.5);
            }

            100% {
                box-shadow: 0 0 15px rgba(66, 153, 225, 0.8);
            }
        }

        @keyframes blink-cursor {

            0%,
            50% {
                border-color: #68d391;
            }

            51%,
            100% {
                border-color: transparent;
            }
        }

        @keyframes data-flow {
            0% {
                opacity: 0;
                transform: translateY(100%) scale(0.5);
            }

            10%,
            90% {
                opacity: 1;
                transform: translateY(0%) scale(1);
            }

            100% {
                opacity: 0;
                transform: translateY(-100%) scale(0.5);
            }
        }

        .result-display {
            /* Adjusted for circular appearance */
            min-width: 250px;
            /* Ensure enough space for a circle */
            min-height: 250px;
            /* Ensure enough space for a circle */
            aspect-ratio: 1 / 1;
            /* Make it a perfect circle */
            border-radius: 50%;
            /* Make it round */
            background-color: rgba(104, 211, 145, 0.15);
            /* Subtle green background */
            border: 2px solid #68d391;
            /* Green border */
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            font-size: 1.8rem;
            font-weight: bold;
            text-align: center;
            color: #68d391;
            /* Green for success */
            opacity: 0;
            transform: scale(0.8);
            /* Start smaller */
            transition: opacity 0.5s ease-out, transform 0.5s ease-out;
            box-shadow: 0 0 20px rgba(104, 211, 145, 0.4);
            /* Green glow */
            position: relative;
            overflow: visible;
        }

        .result-display.active {
            opacity: 1;
            transform: scale(1);
            /* Scale up to normal size */
            animation: result-pop 0.5s ease-out;
            /* Add a pop animation */
        }

        .sparkle-container {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 300px;
            height: 300px;
            pointer-events: none;
            z-index: 30;
        }

        .sparkle {
            position: absolute;
            width: 4px;
            height: 4px;
            background-color: #ffd700;
            border-radius: 50%;
            opacity: 0;
            animation: sparkle-burst 1s ease-out forwards;
        }

        .sparkle:nth-child(2n) {
            background-color: #68d391;
        }

        .sparkle:nth-child(3n) {
            background-color: #4299e1;
        }

        .sparkle:nth-child(4n) {
            background-color: #ed64a6;
        }

        @keyframes result-pop {
            0% {
                transform: scale(0.8);
                opacity: 0;
            }

            50% {
                transform: scale(1.05);
                opacity: 1;
            }

            100% {
                transform: scale(1);
            }
        }

        @keyframes sparkle-burst {
            0% {
                opacity: 1;
                transform: scale(0) rotate(0deg);
            }

            50% {
                opacity: 1;
                transform: scale(1) rotate(180deg);
            }

            100% {
                opacity: 0;
                transform: scale(0.5) rotate(360deg);
            }
        }

        .percentage-number {
            font-size: 3.5rem;
            /* Even larger for emphasis */
            color: #68d391;
            margin-bottom: 0.5rem;
            text-shadow: 0 0 10px rgba(104, 211, 145, 0.6);
            /* Subtle text glow */
            position: relative;
            z-index: 10;
        }

        .typewriter-result {
            font-family: 'Courier New', monospace;
            color: #68d391;
            border-right: 2px solid #68d391;
            animation: blink-cursor 1s infinite;
            white-space: nowrap;
            overflow: hidden;
        }

        /* Buttons styling */
        .btn {
            @apply px-6 py-3 rounded-full font-semibold text-lg transition duration-300 ease-in-out;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border: none;
            cursor: pointer;
            position: relative;
            overflow: hidden;
            background-size: 200% auto;
        }

        .btn:hover {
            box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
        }

        .btn-primary {
            @apply bg-gradient-to-r from-blue-500 to-indigo-600 text-white;
        }

        .btn-primary:hover {
            background-position: right center;
            /* Change the direction of the change here */
        }

        .btn-secondary {
            @apply bg-gradient-to-r from-gray-600 to-gray-700 text-white;
        }

        .btn-secondary:hover {
            background-position: right center;
        }

        .btn:active {
            transform: translateY(1px);
        }

        input[type="file"] {
            display: none;
            /* Hide default file input */
        }

        .custom-file-upload {
            @apply btn btn-secondary;
            display: inline-block;
            cursor: pointer;
        }

        .button-group {
            @apply flex flex-wrap justify-center gap-4 mt-4;
        }

        @media (max-width: 640px) {
            .container {
                padding: 1.5rem;
            }

            .result-display {
                min-width: 200px;
                min-height: 200px;
                font-size: 1.4rem;
            }

            .percentage-number {
                font-size: 2.5rem;
            }

            .btn {
                width: 100%;
                /* Full width buttons on small screens */
            }

            .main-display-area {
                max-width: 400px;
                /* Adjust for smaller screens */
            }

            .canvas-container {
                width: 70%;
                /* Adjust size relative to main-display-area */
                height: 70%;
                /* Adjust size relative to main-display-area */
            }
        }
    </style>
</head>

<body class="bg-gray-900 text-gray-100">
    <div class="container">
        <h1 class="text-4xl font-bold text-center mb-6 text-blue-400">Ethnic Resemblance Analyzer</h1>

        <div class="main-display-area">
            <div id="ethnicGrid" class="ethnic-grid">
                <!-- Ethnic boxes will be dynamically generated here -->
            </div>
            <div class="canvas-container relative">
                <video id="webcamVideo" autoplay playsinline class="hidden"></video>
                <canvas id="faceCanvas" class="bg-gray-800"></canvas>
                <div id="scanningOverlay" class="scanning-overlay"></div>
                <div id="dataStream" class="data-stream"></div>
                <div id="processingMessage" class="processing-message hidden">
                    <div class="processing-header">
                        <div class="spinner"></div>
                        <span id="processingText">Analyzing...</span>
                    </div>
                    <div class="progress-container">
                        <div id="progressBar" class="progress-bar"></div>
                    </div>
                    <div id="typewriterText" class="typewriter-text"></div>
                </div>
            </div>
        </div>

        <div class="button-group">
            <button id="startWebcamBtn" class="btn btn-primary">Start Webcam</button>
            <label for="imageUpload" class="custom-file-upload">Upload Image</label>
            <input type="file" id="imageUpload" accept="image/*">
            <button id="analyzeBtn" class="btn btn-primary" disabled>Analyze Face</button>
        </div>

        <div id="resultDisplay" class="result-display mt-8">
            <!-- Results will be displayed here -->
        </div>
    </div>

    <script>
        const webcamVideo = document.getElementById('webcamVideo');
        const faceCanvas = document.getElementById('faceCanvas');
        const ctx = faceCanvas.getContext('2d');
        const startWebcamBtn = document.getElementById('startWebcamBtn');
        const imageUpload = document.getElementById('imageUpload');
        const analyzeBtn = document.getElementById('analyzeBtn');
        const scanningOverlay = document.getElementById('scanningOverlay');
        const processingMessage = document.getElementById('processingMessage');
        const processingText = document.getElementById('processingText');
        const resultDisplay = document.getElementById('resultDisplay');
        const ethnicGrid = document.getElementById('ethnicGrid');
        const dataStream = document.getElementById('dataStream');
        const progressBar = document.getElementById('progressBar');
        const typewriterText = document.getElementById('typewriterText');

        let stream = null;
        let animationFrameId = null;
        let isAnalyzing = false;
        let processingMessageInterval = null;
        let progressInterval = null;
        let typewriterInterval = null;
        let dataStreamInterval = null;

        const processingMessages = [
            "Analyzing Facial Features...",
            "Comparing Data Points...",
            "Calculating Resemblance...",
            "Processing Complex Algorithms...",
            "Evaluating Biometric Markers..."
        ];

        const typewriterMessages = [
            "Scanning biometric data...",
            "Cross-referencing database...",
            "Analyzing facial geometry...",
            "Computing similarity scores...",
            "Finalizing results..."
        ];

        // Function to generate ethnic boxes
        function generateEthnicBoxes(count) {
            ethnicGrid.innerHTML = ''; // Clear existing boxes
            for (let i = 0; i < count; i++) {
                const box = document.createElement('div');
                box.className = 'ethnic-box';
                // You can add a subtle number or initial here if desired, e.g., box.textContent = (i + 1);
                ethnicGrid.appendChild(box);
            }
        }

        // Set canvas dimensions to match the container's aspect ratio
        function setCanvasDimensions() {
            const container = faceCanvas.parentElement;
            // For a perfect circle, width and height should be equal.
            // We'll use the container's offsetWidth as the base for both.
            faceCanvas.width = container.offsetWidth;
            faceCanvas.height = container.offsetWidth; // Make height equal to width for circle
        }

        window.addEventListener('resize', setCanvasDimensions);
        window.onload = () => {
            setCanvasDimensions();
            generateEthnicBoxes(64); // Generate 64 boxes (8x8 grid) on load
        };

        // Function to start webcam stream
        async function startWebcam() {
            try {
                // Stop any existing stream
                if (stream) {
                    stream.getTracks().forEach(track => track.stop());
                }
                webcamVideo.classList.remove('hidden');
                stream = await navigator.mediaDevices.getUserMedia({ video: true });
                webcamVideo.srcObject = stream;
                webcamVideo.play();

                // Wait for video to load metadata to get dimensions
                webcamVideo.onloadedmetadata = () => {
                    drawVideoToCanvas();
                    analyzeBtn.disabled = false; // Enable analyze button
                };
            } catch (err) {
                console.error("Error accessing webcam: ", err);
                resultDisplay.innerHTML = `<span class="text-red-400">Error: Could not access webcam. Please allow camera access.</span>`;
                resultDisplay.classList.add('active');
            }
        }

        // Function to continuously draw video frames to canvas
        function drawVideoToCanvas() {
            if (webcamVideo.readyState === webcamVideo.HAVE_ENOUGH_DATA) {
                // To draw video into a circular canvas, we need to clip it.
                ctx.save(); // Save the current state
                ctx.beginPath();
                // Draw a circle path
                ctx.arc(faceCanvas.width / 2, faceCanvas.height / 2, Math.min(faceCanvas.width, faceCanvas.height) / 2, 0, Math.PI * 2, true);
                ctx.closePath();
                ctx.clip(); // Clip to the circle

                // Draw the video frame, ensuring it covers the circle
                const videoAspectRatio = webcamVideo.videoWidth / webcamVideo.videoHeight;
                const canvasAspectRatio = faceCanvas.width / faceCanvas.height;

                let sx, sy, sWidth, sHeight; // Source rectangle
                let dx, dy, dWidth, dHeight; // Destination rectangle

                // Calculate source and destination to cover the canvas while maintaining aspect ratio
                if (videoAspectRatio > canvasAspectRatio) {
                    // Video is wider than canvas, crop video horizontally
                    sHeight = webcamVideo.videoHeight;
                    sWidth = sHeight * canvasAspectRatio;
                    sx = (webcamVideo.videoWidth - sWidth) / 2;
                    sy = 0;
                } else {
                    // Video is taller than canvas, crop video vertically
                    sWidth = webcamVideo.videoWidth;
                    sHeight = sWidth / canvasAspectRatio;
                    sy = (webcamVideo.videoHeight - sHeight) / 2;
                    sx = 0;
                }

                dx = 0;
                dy = 0;
                dWidth = faceCanvas.width;
                dHeight = faceCanvas.height;

                ctx.drawImage(webcamVideo, sx, sy, sWidth, sHeight, dx, dy, dWidth, dHeight);
                ctx.restore(); // Restore the canvas state (remove clipping path)
            }
            animationFrameId = requestAnimationFrame(drawVideoToCanvas);
        }

        // Function to handle image upload
        imageUpload.addEventListener('change', (event) => {
            if (stream) { // Stop webcam if active
                stream.getTracks().forEach(track => track.stop());
                webcamVideo.classList.add('hidden');
                cancelAnimationFrame(animationFrameId);
            }

            const file = event.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    const img = new Image();
                    img.onload = () => {
                        ctx.clearRect(0, 0, faceCanvas.width, faceCanvas.height);

                        // To draw image into a circular canvas, we need to clip it.
                        ctx.save(); // Save the current state
                        ctx.beginPath();
                        // Draw a circle path
                        ctx.arc(faceCanvas.width / 2, faceCanvas.height / 2, Math.min(faceCanvas.width, faceCanvas.height) / 2, 0, Math.PI * 2, true);
                        ctx.closePath();
                        ctx.clip(); // Clip to the circle

                        // Draw the image, ensuring it covers the circle
                        const imgAspectRatio = img.width / img.height;
                        const canvasAspectRatio = faceCanvas.width / faceCanvas.height;

                        let sx, sy, sWidth, sHeight; // Source rectangle
                        let dx, dy, dWidth, dHeight; // Destination rectangle

                        // Calculate source and destination to cover the canvas while maintaining aspect ratio
                        if (imgAspectRatio > canvasAspectRatio) {
                            // Image is wider than canvas, crop image horizontally
                            sHeight = img.height;
                            sWidth = sHeight * canvasAspectRatio;
                            sx = (img.width - sWidth) / 2;
                            sy = 0;
                        } else {
                            // Image is taller than canvas, crop image vertically
                            sWidth = img.width;
                            sHeight = sWidth / canvasAspectRatio;
                            sy = (img.height - sHeight) / 2;
                            sx = 0;
                        }

                        dx = 0;
                        dy = 0;
                        dWidth = faceCanvas.width;
                        dHeight = faceCanvas.height;

                        ctx.drawImage(img, sx, sy, sWidth, sHeight, dx, dy, dWidth, dHeight);
                        ctx.restore(); // Restore the canvas state (remove clipping path)

                        analyzeBtn.disabled = false; // Enable analyze button
                    };
                    img.src = e.target.result;
                };
                reader.readAsDataURL(file);
            }
        });

        // Function to create data stream particles
        function createDataStream() {
            dataStream.innerHTML = ''; // Clear existing particles

            for (let i = 0; i < 15; i++) {
                const particle = document.createElement('div');
                particle.className = 'data-particle';

                // Random position around the circle
                const angle = (i / 15) * 2 * Math.PI;
                const radius = 120;
                const x = 50 + (radius * Math.cos(angle)) / 3;
                const y = 50 + (radius * Math.sin(angle)) / 3;

                particle.style.left = x + '%';
                particle.style.top = y + '%';
                particle.style.animationDelay = (i * 0.1) + 's';

                dataStream.appendChild(particle);
            }
        }

        // Function to start scanning animation
        function startScanningAnimation() {
            scanningOverlay.classList.add('active');
            createDataStream();
        }

        // Function to stop scanning animation
        function stopScanningAnimation() {
            scanningOverlay.classList.remove('active');
            dataStream.innerHTML = '';
        }

        // Function to animate progress bar
        function animateProgressBar() {
            let progress = 0;
            progressBar.style.width = '0%';

            progressInterval = setInterval(() => {
                progress += Math.random() * 15 + 5; // Random increment between 5-20%
                if (progress > 100) progress = 100;

                progressBar.style.width = progress + '%';

                if (progress >= 100) {
                    clearInterval(progressInterval);
                }
            }, 200);
        }

        // Function to animate typewriter text
        function animateTypewriter() {
            let messageIndex = 0;
            let charIndex = 0;
            let currentMessage = typewriterMessages[messageIndex];

            typewriterText.textContent = '';

            typewriterInterval = setInterval(() => {
                if (charIndex < currentMessage.length) {
                    typewriterText.textContent += currentMessage[charIndex];
                    charIndex++;
                } else {
                    // Move to next message after a pause
                    setTimeout(() => {
                        messageIndex = (messageIndex + 1) % typewriterMessages.length;
                        currentMessage = typewriterMessages[messageIndex];
                        charIndex = 0;
                        typewriterText.textContent = '';
                    }, 1000);
                }
            }, 50); // Type speed
        }

        // Function to show processing message and cycle text
        function showProcessingMessage() {
            processingMessage.classList.remove('hidden');
            processingMessage.classList.add('active');

            let messageIndex = 0;
            processingText.textContent = processingMessages[messageIndex];

            // Start all animations
            animateProgressBar();
            animateTypewriter();

            processingMessageInterval = setInterval(() => {
                messageIndex = (messageIndex + 1) % processingMessages.length;
                processingText.textContent = processingMessages[messageIndex];
            }, 800); // Change message every 0.8 seconds
        }

        // Function to hide processing message
        function hideProcessingMessage() {
            clearInterval(processingMessageInterval);
            clearInterval(progressInterval);
            clearInterval(typewriterInterval);

            processingMessage.classList.remove('active');
            // Use setTimeout to allow fade-out transition before hiding
            setTimeout(() => {
                processingMessage.classList.add('hidden');
                progressBar.style.width = '0%';
                typewriterText.textContent = '';
            }, 300);
        }

        // Function to create sparkle effect
        function createSparkles() {
            const sparkleContainer = document.createElement('div');
            sparkleContainer.className = 'sparkle-container';

            for (let i = 0; i < 12; i++) {
                const sparkle = document.createElement('div');
                sparkle.className = 'sparkle';

                // Random position around the result circle
                const angle = (i / 12) * 2 * Math.PI;
                const radius = 100 + Math.random() * 50;
                const x = 50 + (radius * Math.cos(angle)) / 3;
                const y = 50 + (radius * Math.sin(angle)) / 3;

                sparkle.style.left = x + '%';
                sparkle.style.top = y + '%';
                sparkle.style.animationDelay = (i * 0.1) + 's';

                sparkleContainer.appendChild(sparkle);
            }

            resultDisplay.appendChild(sparkleContainer);

            // Remove sparkles after animation
            setTimeout(() => {
                if (sparkleContainer.parentNode) {
                    sparkleContainer.remove();
                }
            }, 1500);
        }

        // Function to animate typewriter result text
        function animateResultTypewriter(text, element) {
            let charIndex = 0;
            element.textContent = '';

            const typeInterval = setInterval(() => {
                if (charIndex < text.length) {
                    element.textContent += text[charIndex];
                    charIndex++;
                } else {
                    clearInterval(typeInterval);
                    // Remove cursor after typing is complete
                    setTimeout(() => {
                        element.style.borderRight = 'none';
                    }, 1000);
                }
            }, 50);
        }

        // Function to reveal result with animation
        function revealResult(percentage, group) {
            const percentageSpan = document.createElement('span');
            percentageSpan.className = 'percentage-number';
            percentageSpan.textContent = '0%'; // Start from 0 for counting animation

            const groupText = document.createElement('span');
            groupText.className = 'typewriter-result';

            resultDisplay.innerHTML = ''; // Clear previous content
            resultDisplay.appendChild(percentageSpan);
            resultDisplay.appendChild(groupText);
            resultDisplay.classList.add('active');

            // Create sparkle effect
            setTimeout(() => {
                createSparkles();
            }, 200);

            // Counter animation for percentage
            let currentPercentage = 0;
            const counterInterval = setInterval(() => {
                if (currentPercentage < percentage) {
                    currentPercentage++;
                    percentageSpan.textContent = `${currentPercentage}%`;
                } else {
                    clearInterval(counterInterval);
                    // Start typewriter animation for group text after percentage is done
                    setTimeout(() => {
                        animateResultTypewriter(`resemblance to the people of ${group}.`, groupText);
                    }, 300);
                }
            }, 20); // Adjust speed of counting
        }

        // Function to hide result
        function hideResult() {
            resultDisplay.classList.remove('active');
            setTimeout(() => {
                resultDisplay.innerHTML = '';
            }, 500); // Allow transition to complete
        }

        // Simulate facial analysis
        async function analyzeFace() {
            if (isAnalyzing) return; // Prevent multiple analyses
            isAnalyzing = true;
            analyzeBtn.disabled = true;
            startWebcamBtn.disabled = true;
            imageUpload.disabled = true;

            hideResult(); // Hide previous result if any

            // Phase 1: Pre-Analysis Animation (Scanning Face) - 2-3 seconds
            startScanningAnimation();
            await new Promise(resolve => setTimeout(resolve, 2500));

            // Phase 2: During Analysis Animation (Processing Data) - 2-3 seconds
            showProcessingMessage();
            await new Promise(resolve => setTimeout(resolve, 3000));

            // Phase 3: Stop processing animations
            stopScanningAnimation();
            hideProcessingMessage();

            // Brief pause before result reveal
            await new Promise(resolve => setTimeout(resolve, 500));

            // Phase 4: Result Reveal Animation (The Big Reveal) - 1 second
            const ethnicGroups = ["Jogja", "Sunda", "Batak", "Minang", "Bugis", "Dayak", "Jawa", "Melayu", "Aceh", "Bali"];
            const randomPercentage = Math.floor(Math.random() * (95 - 50 + 1)) + 50; // Between 50 and 95
            const randomGroup = ethnicGroups[Math.floor(Math.random() * ethnicGroups.length)];

            revealResult(randomPercentage, randomGroup);

            isAnalyzing = false;
            analyzeBtn.disabled = false;
            startWebcamBtn.disabled = false;
            imageUpload.disabled = false;
        }

        // Event Listeners
        startWebcamBtn.addEventListener('click', startWebcam);
        analyzeBtn.addEventListener('click', analyzeFace);
    </script>
</body>

</html>