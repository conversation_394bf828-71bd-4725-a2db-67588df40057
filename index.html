<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ethnic Resemblance Analyzer</title>
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #1a202c; /* Dark background */
            color: #e2e8f0; /* Light text */
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
            box-sizing: border-box;
        }

        .container {
            background-color: #2d3748; /* Slightly lighter dark background for container */
            border-radius: 1.5rem; /* More rounded corners */
            box-shadow: 0 10px 15px rgba(0, 0, 0, 0.2);
            padding: 2.5rem;
            max-width: 900px;
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 1.5rem;
        }

        .main-display-area {
            position: relative;
            width: 100%;
            max-width: 600px; /* Overall size of the display area including the grid */
            aspect-ratio: 1 / 1; /* Keep it square for the grid */
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .ethnic-grid {
            display: grid;
            grid-template-columns: repeat(8, 1fr); /* 8 columns */
            grid-template-rows: repeat(8, 1fr); /* 8 rows */
            width: 100%;
            height: 100%;
            gap: 5px; /* Small gap between boxes */
            padding: 10px; /* Padding inside the grid container */
            box-sizing: border-box;
        }

        .ethnic-box {
            width: 100%;
            height: 100%;
            aspect-ratio: 1 / 1;
            border-radius: 50%;
            background-color: rgba(74, 85, 104, 0.5); /* Subtle gray background */
            border: 1px solid rgba(113, 128, 150, 0.3);
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 0.7rem;
            color: #a0aec0;
            overflow: hidden; /* Ensure content is clipped if any */
            opacity: 0;
            animation: fade-in-scale 0.5s forwards; /* Animation for each box */
        }

        @keyframes fade-in-scale {
            from { opacity: 0; transform: scale(0.8); }
            to { opacity: 1; transform: scale(1); }
        }

        /* Delay animation for each box */
        .ethnic-box:nth-child(even) { animation-delay: 0.1s; }
        .ethnic-box:nth-child(3n) { animation-delay: 0.2s; }
        .ethnic-box:nth-child(5n) { animation-delay: 0.3s; }


        .canvas-container {
            position: absolute;
            width: 60%; /* Adjust size relative to main-display-area */
            height: 60%; /* Adjust size relative to main-display-area */
            max-width: 400px; /* Adjusted max-width for a better circular appearance */
            aspect-ratio: 1 / 1; /* Make it a perfect circle */
            background-color: #000;
            border-radius: 50%; /* Make it round */
            overflow: hidden; /* Crucial for clipping content to the circle */
            display: flex;
            justify-content: center;
            align-items: center;
            border: 2px solid #4a5568;
            z-index: 50; /* Ensure it's above the grid */
            box-shadow: 0 0 30px rgba(66, 153, 225, 0.6); /* Add a glow */
        }

        video, canvas {
            width: 100%;
            height: 100%;
            object-fit: cover; /* Ensures video/image covers the circular container */
            border-radius: 50%; /* Apply to content as well for consistency */
        }

        .scanning-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border: 4px solid transparent;
            box-sizing: border-box;
            border-radius: 50%; /* Make it round */
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.3s ease-in-out;
            z-index: 10;
        }

        .scanning-overlay.active {
            opacity: 1;
            animation: pulse-border 1.5s infinite alternate; /* Keep pulse */
        }

        /* New scan line animation */
        .scanning-overlay::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(to bottom, transparent 0%, transparent 45%, rgba(66, 153, 225, 0.7) 50%, transparent 55%, transparent 100%);
            background-size: 100% 200%;
            background-position: 0% 0%;
            animation: scan-line-v2 2s infinite linear;
            opacity: 0;
            transition: opacity 0.3s ease-in-out;
            border-radius: 50%; /* Make scan line round */
        }

        .scanning-overlay.active::before {
            opacity: 1;
        }

        @keyframes pulse-border {
            0% { border-color: rgba(66, 153, 225, 0.5); box-shadow: 0 0 15px rgba(66, 153, 225, 0.5); }
            100% { border-color: rgba(66, 153, 225, 1); box-shadow: 0 0 25px rgba(66, 153, 225, 1); }
        }

        @keyframes scan-line-v2 {
            0% { background-position: 0% 0%; }
            100% { background-position: 0% 100%; }
        }

        .processing-message {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: rgba(0, 0, 0, 0.7);
            padding: 1rem 2rem;
            border-radius: 0.75rem;
            font-size: 1.5rem;
            font-weight: bold;
            color: #4299e1;
            opacity: 0;
            transition: opacity 0.3s ease-in-out;
            z-index: 20;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            text-align: center;
            min-width: 250px; /* Ensure enough width for messages */
            justify-content: center;
        }

        .processing-message.active {
            opacity: 1;
        }

        .spinner {
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid #4299e1;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .result-display {
            /* Adjusted for circular appearance */
            min-width: 250px; /* Ensure enough space for a circle */
            min-height: 250px; /* Ensure enough space for a circle */
            aspect-ratio: 1 / 1; /* Make it a perfect circle */
            border-radius: 50%; /* Make it round */
            background-color: rgba(104, 211, 145, 0.15); /* Subtle green background */
            border: 2px solid #68d391; /* Green border */
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            font-size: 1.8rem;
            font-weight: bold;
            text-align: center;
            color: #68d391; /* Green for success */
            opacity: 0;
            transform: scale(0.8); /* Start smaller */
            transition: opacity 0.5s ease-out, transform 0.5s ease-out;
            box-shadow: 0 0 20px rgba(104, 211, 145, 0.4); /* Green glow */
        }

        .result-display.active {
            opacity: 1;
            transform: scale(1); /* Scale up to normal size */
            animation: result-pop 0.5s ease-out; /* Add a pop animation */
        }

        @keyframes result-pop {
            0% { transform: scale(0.8); opacity: 0; }
            50% { transform: scale(1.05); opacity: 1; }
            100% { transform: scale(1); }
        }

        .percentage-number {
            font-size: 3.5rem; /* Even larger for emphasis */
            color: #68d391;
            margin-bottom: 0.5rem;
            text-shadow: 0 0 10px rgba(104, 211, 145, 0.6); /* Subtle text glow */
        }

        /* Buttons styling */
        .btn {
            @apply px-6 py-3 rounded-full font-semibold text-lg transition duration-300 ease-in-out;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border: none;
            cursor: pointer;
            position: relative;
            overflow: hidden;
            background-size: 200% auto;
        }

        .btn:hover {
            box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
        }

        .btn-primary {
            @apply bg-gradient-to-r from-blue-500 to-indigo-600 text-white;
        }
        .btn-primary:hover {
            background-position: right center; /* Change the direction of the change here */
        }

        .btn-secondary {
            @apply bg-gradient-to-r from-gray-600 to-gray-700 text-white;
        }
        .btn-secondary:hover {
            background-position: right center;
        }

        .btn:active {
            transform: translateY(1px);
        }

        input[type="file"] {
            display: none; /* Hide default file input */
        }

        .custom-file-upload {
            @apply btn btn-secondary;
            display: inline-block;
            cursor: pointer;
        }

        .button-group {
            @apply flex flex-wrap justify-center gap-4 mt-4;
        }

        @media (max-width: 640px) {
            .container {
                padding: 1.5rem;
            }
            .result-display {
                min-width: 200px;
                min-height: 200px;
                font-size: 1.4rem;
            }
            .percentage-number {
                font-size: 2.5rem;
            }
            .btn {
                width: 100%; /* Full width buttons on small screens */
            }
            .main-display-area {
                max-width: 400px; /* Adjust for smaller screens */
            }
            .canvas-container {
                width: 70%; /* Adjust size relative to main-display-area */
                height: 70%; /* Adjust size relative to main-display-area */
            }
        }
    </style>
</head>
<body class="bg-gray-900 text-gray-100">
    <div class="container">
        <h1 class="text-4xl font-bold text-center mb-6 text-blue-400">Ethnic Resemblance Analyzer</h1>

        <div class="main-display-area">
            <div id="ethnicGrid" class="ethnic-grid">
                <!-- Ethnic boxes will be dynamically generated here -->
            </div>
            <div class="canvas-container relative">
                <video id="webcamVideo" autoplay playsinline class="hidden"></video>
                <canvas id="faceCanvas" class="bg-gray-800"></canvas>
                <div id="scanningOverlay" class="scanning-overlay"></div>
                <div id="processingMessage" class="processing-message hidden">
                    <div class="spinner"></div>
                    <span id="processingText">Analyzing...</span>
                </div>
            </div>
        </div>

        <div class="button-group">
            <button id="startWebcamBtn" class="btn btn-primary">Start Webcam</button>
            <label for="imageUpload" class="custom-file-upload">Upload Image</label>
            <input type="file" id="imageUpload" accept="image/*">
            <button id="analyzeBtn" class="btn btn-primary" disabled>Analyze Face</button>
        </div>

        <div id="resultDisplay" class="result-display mt-8">
            <!-- Results will be displayed here -->
        </div>
    </div>

    <script>
        const webcamVideo = document.getElementById('webcamVideo');
        const faceCanvas = document.getElementById('faceCanvas');
        const ctx = faceCanvas.getContext('2d');
        const startWebcamBtn = document.getElementById('startWebcamBtn');
        const imageUpload = document.getElementById('imageUpload');
        const analyzeBtn = document.getElementById('analyzeBtn');
        const scanningOverlay = document.getElementById('scanningOverlay');
        const processingMessage = document.getElementById('processingMessage');
        const processingText = document.getElementById('processingText');
        const resultDisplay = document.getElementById('resultDisplay');
        const ethnicGrid = document.getElementById('ethnicGrid');

        let stream = null;
        let animationFrameId = null;
        let isAnalyzing = false;
        let processingMessageInterval = null;

        const processingMessages = [
            "Analyzing Facial Features...",
            "Comparing Data Points...",
            "Calculating Resemblance...",
            "Processing Complex Algorithms...",
            "Evaluating Biometric Markers..."
        ];

        // Function to generate ethnic boxes
        function generateEthnicBoxes(count) {
            ethnicGrid.innerHTML = ''; // Clear existing boxes
            for (let i = 0; i < count; i++) {
                const box = document.createElement('div');
                box.className = 'ethnic-box';
                // You can add a subtle number or initial here if desired, e.g., box.textContent = (i + 1);
                ethnicGrid.appendChild(box);
            }
        }

        // Set canvas dimensions to match the container's aspect ratio
        function setCanvasDimensions() {
            const container = faceCanvas.parentElement;
            // For a perfect circle, width and height should be equal.
            // We'll use the container's offsetWidth as the base for both.
            faceCanvas.width = container.offsetWidth;
            faceCanvas.height = container.offsetWidth; // Make height equal to width for circle
        }

        window.addEventListener('resize', setCanvasDimensions);
        window.onload = () => {
            setCanvasDimensions();
            generateEthnicBoxes(64); // Generate 64 boxes (8x8 grid) on load
        };

        // Function to start webcam stream
        async function startWebcam() {
            try {
                // Stop any existing stream
                if (stream) {
                    stream.getTracks().forEach(track => track.stop());
                }
                webcamVideo.classList.remove('hidden');
                stream = await navigator.mediaDevices.getUserMedia({ video: true });
                webcamVideo.srcObject = stream;
                webcamVideo.play();

                // Wait for video to load metadata to get dimensions
                webcamVideo.onloadedmetadata = () => {
                    drawVideoToCanvas();
                    analyzeBtn.disabled = false; // Enable analyze button
                };
            } catch (err) {
                console.error("Error accessing webcam: ", err);
                resultDisplay.innerHTML = `<span class="text-red-400">Error: Could not access webcam. Please allow camera access.</span>`;
                resultDisplay.classList.add('active');
            }
        }

        // Function to continuously draw video frames to canvas
        function drawVideoToCanvas() {
            if (webcamVideo.readyState === webcamVideo.HAVE_ENOUGH_DATA) {
                // To draw video into a circular canvas, we need to clip it.
                ctx.save(); // Save the current state
                ctx.beginPath();
                // Draw a circle path
                ctx.arc(faceCanvas.width / 2, faceCanvas.height / 2, Math.min(faceCanvas.width, faceCanvas.height) / 2, 0, Math.PI * 2, true);
                ctx.closePath();
                ctx.clip(); // Clip to the circle

                // Draw the video frame, ensuring it covers the circle
                const videoAspectRatio = webcamVideo.videoWidth / webcamVideo.videoHeight;
                const canvasAspectRatio = faceCanvas.width / faceCanvas.height;

                let sx, sy, sWidth, sHeight; // Source rectangle
                let dx, dy, dWidth, dHeight; // Destination rectangle

                // Calculate source and destination to cover the canvas while maintaining aspect ratio
                if (videoAspectRatio > canvasAspectRatio) {
                    // Video is wider than canvas, crop video horizontally
                    sHeight = webcamVideo.videoHeight;
                    sWidth = sHeight * canvasAspectRatio;
                    sx = (webcamVideo.videoWidth - sWidth) / 2;
                    sy = 0;
                } else {
                    // Video is taller than canvas, crop video vertically
                    sWidth = webcamVideo.videoWidth;
                    sHeight = sWidth / canvasAspectRatio;
                    sy = (webcamVideo.videoHeight - sHeight) / 2;
                    sx = 0;
                }

                dx = 0;
                dy = 0;
                dWidth = faceCanvas.width;
                dHeight = faceCanvas.height;

                ctx.drawImage(webcamVideo, sx, sy, sWidth, sHeight, dx, dy, dWidth, dHeight);
                ctx.restore(); // Restore the canvas state (remove clipping path)
            }
            animationFrameId = requestAnimationFrame(drawVideoToCanvas);
        }

        // Function to handle image upload
        imageUpload.addEventListener('change', (event) => {
            if (stream) { // Stop webcam if active
                stream.getTracks().forEach(track => track.stop());
                webcamVideo.classList.add('hidden');
                cancelAnimationFrame(animationFrameId);
            }

            const file = event.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    const img = new Image();
                    img.onload = () => {
                        ctx.clearRect(0, 0, faceCanvas.width, faceCanvas.height);

                        // To draw image into a circular canvas, we need to clip it.
                        ctx.save(); // Save the current state
                        ctx.beginPath();
                        // Draw a circle path
                        ctx.arc(faceCanvas.width / 2, faceCanvas.height / 2, Math.min(faceCanvas.width, faceCanvas.height) / 2, 0, Math.PI * 2, true);
                        ctx.closePath();
                        ctx.clip(); // Clip to the circle

                        // Draw the image, ensuring it covers the circle
                        const imgAspectRatio = img.width / img.height;
                        const canvasAspectRatio = faceCanvas.width / faceCanvas.height;

                        let sx, sy, sWidth, sHeight; // Source rectangle
                        let dx, dy, dWidth, dHeight; // Destination rectangle

                        // Calculate source and destination to cover the canvas while maintaining aspect ratio
                        if (imgAspectRatio > canvasAspectRatio) {
                            // Image is wider than canvas, crop image horizontally
                            sHeight = img.height;
                            sWidth = sHeight * canvasAspectRatio;
                            sx = (img.width - sWidth) / 2;
                            sy = 0;
                        } else {
                            // Image is taller than canvas, crop image vertically
                            sWidth = img.width;
                            sHeight = sWidth / canvasAspectRatio;
                            sy = (img.height - sHeight) / 2;
                            sx = 0;
                        }

                        dx = 0;
                        dy = 0;
                        dWidth = faceCanvas.width;
                        dHeight = faceCanvas.height;

                        ctx.drawImage(img, sx, sy, sWidth, sHeight, dx, dy, dWidth, dHeight);
                        ctx.restore(); // Restore the canvas state (remove clipping path)

                        analyzeBtn.disabled = false; // Enable analyze button
                    };
                    img.src = e.target.result;
                };
                reader.readAsDataURL(file);
            }
        });

        // Function to start scanning animation
        function startScanningAnimation() {
            scanningOverlay.classList.add('active');
        }

        // Function to stop scanning animation
        function stopScanningAnimation() {
            scanningOverlay.classList.remove('active');
        }

        // Function to show processing message and cycle text
        function showProcessingMessage() {
            processingMessage.classList.remove('hidden');
            processingMessage.classList.add('active');
            let messageIndex = 0;
            processingText.textContent = processingMessages[messageIndex];
            processingMessageInterval = setInterval(() => {
                messageIndex = (messageIndex + 1) % processingMessages.length;
                processingText.textContent = processingMessages[messageIndex];
            }, 800); // Change message every 0.8 seconds
        }

        // Function to hide processing message
        function hideProcessingMessage() {
            clearInterval(processingMessageInterval);
            processingMessage.classList.remove('active');
            // Use setTimeout to allow fade-out transition before hiding
            setTimeout(() => {
                processingMessage.classList.add('hidden');
            }, 300);
        }

        // Function to reveal result with animation
        function revealResult(percentage, group) {
            const percentageSpan = document.createElement('span');
            percentageSpan.className = 'percentage-number';
            percentageSpan.textContent = '0%'; // Start from 0 for counting animation

            const groupText = document.createElement('span');
            groupText.textContent = `resemblance to the people of ${group}.`;

            resultDisplay.innerHTML = ''; // Clear previous content
            resultDisplay.appendChild(percentageSpan);
            resultDisplay.appendChild(groupText);
            resultDisplay.classList.add('active');

            // Counter animation for percentage
            let currentPercentage = 0;
            const counterInterval = setInterval(() => {
                if (currentPercentage < percentage) {
                    currentPercentage++;
                    percentageSpan.textContent = `${currentPercentage}%`;
                } else {
                    clearInterval(counterInterval);
                }
            }, 20); // Adjust speed of counting
        }

        // Function to hide result
        function hideResult() {
            resultDisplay.classList.remove('active');
            setTimeout(() => {
                resultDisplay.innerHTML = '';
            }, 500); // Allow transition to complete
        }

        // Simulate facial analysis
        async function analyzeFace() {
            if (isAnalyzing) return; // Prevent multiple analyses
            isAnalyzing = true;
            analyzeBtn.disabled = true;
            startWebcamBtn.disabled = true;
            imageUpload.disabled = true;

            hideResult(); // Hide previous result if any

            startScanningAnimation();
            showProcessingMessage();

            // Simulate analysis time
            await new Promise(resolve => setTimeout(resolve, 3500)); // Increased time for more animation

            stopScanningAnimation();
            hideProcessingMessage();

            // Generate random result
            const ethnicGroups = ["Jogja", "Sunda", "Batak", "Minang", "Bugis", "Dayak", "Jawa", "Melayu", "Aceh", "Bali"];
            const randomPercentage = Math.floor(Math.random() * (95 - 50 + 1)) + 50; // Between 50 and 95
            const randomGroup = ethnicGroups[Math.floor(Math.random() * ethnicGroups.length)];

            revealResult(randomPercentage, randomGroup);

            isAnalyzing = false;
            analyzeBtn.disabled = false;
            startWebcamBtn.disabled = false;
            imageUpload.disabled = false;
        }

        // Event Listeners
        startWebcamBtn.addEventListener('click', startWebcam);
        analyzeBtn.addEventListener('click', analyzeFace);
    </script>
</body>
</html>
